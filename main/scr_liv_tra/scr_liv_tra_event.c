#include "lvgl.h"
#include "../gui.h"
#include "../styles.h"
#include "scr_liv_tra.h"
#include "esp_log.h"
#include <stdio.h>

static const char *TAG = "SCR_LIV_TRA_EVENT";

#ifdef __cplusplus
extern "C"
{
#endif
      extern void sendMessage(const char *message);
#ifdef __cplusplus
}
#endif

// Gestionnaires d'événements
void volume_container_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      lv_obj_t *target = lv_event_get_target(e);
      lv_obj_t *current_target = lv_event_get_current_target(e);

      if ((target == current_target ||
           lv_obj_has_flag(target, LV_OBJ_FLAG_CLICKABLE)))
      {
            if (code == LV_EVENT_SHORT_CLICKED)
            {
                  ESP_LOGI(TAG, "Volume container click - initialisation");
                  sendMessage("t,volinit,0");
                  lv_event_stop_bubbling(e);
            }
            else if (code == LV_EVENT_LONG_PRESSED)
            {
                  ESP_LOGI(TAG, "Volume container long press - learn");
                  sendMessage("t,le,-1");
                  lv_event_stop_bubbling(e);
            }
      }
}

void pan_container_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      lv_obj_t *target = lv_event_get_target(e);
      lv_obj_t *current_target = lv_event_get_current_target(e);

      if ((target == current_target ||
           lv_obj_has_flag(target, LV_OBJ_FLAG_CLICKABLE)))
      {
            if (code == LV_EVENT_SHORT_CLICKED)
            {
                  ESP_LOGI(TAG, "Pan container click - initialisation");
                  sendMessage("t,paninit,0");
                  lv_event_stop_bubbling(e);
            }
            else if (code == LV_EVENT_LONG_PRESSED)
            {
                  ESP_LOGI(TAG, "Pan container long press - learn");
                  sendMessage("t,le,-2");
                  lv_event_stop_bubbling(e);
            }
      }
}

void mute_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED)
      {
            ESP_LOGI(TAG, "Mute button click");
            sendMessage("t,mut,0");

      }
      else if (code == LV_EVENT_LONG_PRESSED)
      {
            ESP_LOGI(TAG, "Mute button long press - learn");
            sendMessage("t,le,-7");
      }
}

void solo_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED)
      {
            ESP_LOGI(TAG, "Solo button click");
            sendMessage("t,sol,0");
      }
      else if (code == LV_EVENT_LONG_PRESSED)
      {
            ESP_LOGI(TAG, "Solo button long press - learn");
            sendMessage("t,le,-8");
      }
}

void arm_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED)
      {
            ESP_LOGI(TAG, "Arm button click");
            sendMessage("t,arm,0");
      }
}

void middle_band_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED)
      {
            ESP_LOGI(TAG, "Middle band click - track info");
            sendMessage("TRACK_INFO_CLICKED");
      }
}

void send_container_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      lv_obj_t *target = lv_event_get_target(e);
      lv_obj_t *current_target = lv_event_get_current_target(e);

      if (code == LV_EVENT_LONG_PRESSED &&
          (target == current_target ||
           lv_obj_has_flag(target, LV_OBJ_FLAG_CLICKABLE)))
      {
            // Récupérer l'index du container
            for (int i = 0; i < 4; i++)
            {
                  if (ui.top_band.sends[i].container == current_target)
                  {
                        char message[10];
                        snprintf(message, sizeof(message), "t,le,%d", i);
                        ESP_LOGI(TAG, "Send container %d long press - learn", i);
                        sendMessage(message);
                        break;
                  }
            }
            lv_event_stop_bubbling(e);
      }
}

// Event callback for the popup background
void relatives_popup_button_event_cb(lv_event_t *e) {
      lv_obj_t *target = lv_event_get_target(e);
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_SHORT_CLICKED) {
            // If clicking on the background, close the popup
            if (target == ui.relatives_popup.background_container) {
                  hide_relatives_popup();
            }
      }
}

// Event callback for the track buttons in the popup
void track_button_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_CLICKED) {
            // Get the user data (track index)
            int track_index = (int)(intptr_t)lv_event_get_user_data(e);

            // Get the popup title
            const char *popup_title = lv_label_get_text(ui.relatives_popup.title_label);

            // Send message in format "t,tw,<indexdubouton>" or "t,td,<indexdubouton>"
            char message[20];
            if (strcmp(popup_title, "Daughters Tracks") == 0) {
                  snprintf(message, sizeof(message), "t,td,%d", track_index);
                  ESP_LOGI(TAG, "Selected daughter track %d from popup, sending: %s", track_index, message);
            } else {
                  snprintf(message, sizeof(message), "t,tw,%d", track_index);
                  ESP_LOGI(TAG, "Selected track %d from popup, sending: %s", track_index, message);
            }
            sendMessage(message);

            // Close the popup after selection
            hide_relatives_popup();
      }
}

// Event callback for the sisters container
void sisters_container_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Sisters container clicked - showing popup");
            // Update the popup title to indicate we're showing sisters tracks
            lv_label_set_text(ui.relatives_popup.title_label, "Sisters Tracks");

            // Here you would update the track buttons with actual sisters track data
            // For now, we'll just show the popup with default track names

            // Show the popup
            show_relatives_popup();
      }
}

// Event callback for the daughters container
void daughters_container_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Daughters container clicked - showing popup");
            // Update the popup title to indicate we're showing daughters tracks
            lv_label_set_text(ui.relatives_popup.title_label, "Daughters Tracks");

            // Here you would update the track buttons with actual daughters track data
            // For now, we'll just show the popup with default track names

            // Show the popup
            show_relatives_popup();
      }
}

// Event callback for the parent container
void parent_container_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Parent container clicked - showing popup");
            char message[10];
            snprintf(message, sizeof(message), "t,pa,0");
            sendMessage(message);            
      }
}

void metro_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Metro button clicked");
            char message[10];
            snprintf(message, sizeof(message), "t,me,0");
            sendMessage(message);            
      }
}

static lv_point_t loop_press_point; // Variable pour stocker la position au moment de LV_EVENT_PRESSED
static uint32_t loop_press_time; // Variable pour stocker le temps au moment de l'appui

void loop_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_PRESSED) {
          // Stocker la position et le temps au moment de l'appui
          lv_indev_get_point(lv_indev_get_act(), &loop_press_point);
          loop_press_time = lv_tick_get();
      } else if (code == LV_EVENT_RELEASED) {
          lv_point_t release_point;
          lv_indev_get_point(lv_indev_get_act(), &release_point);

          uint32_t release_time = lv_tick_get();
          uint32_t press_duration = release_time - loop_press_time;

          int16_t diff_x = abs(release_point.x - loop_press_point.x);
          int16_t diff_y = abs(release_point.y - loop_press_point.y);

          // Définir des seuils pour différencier clic, swipe et appui long
          const int16_t click_threshold = 10; // Seuil de pixels pour considérer un mouvement comme un clic
          const int16_t swipe_threshold_x = 30; // Seuil de pixels pour considérer un mouvement horizontal comme un swipe
          const uint32_t long_press_threshold = 800; // Seuil de temps en ms pour considérer un appui long

          // Vérifier d'abord si c'est un appui long (peu de mouvement + temps long)
          if (press_duration >= long_press_threshold && diff_x < click_threshold && diff_y < click_threshold) {
              // C'est un appui long
              ESP_LOGI(TAG, "Loop button long pressed - showing loop settings popup");
              show_loop_settings_popup();
          } else if (diff_x < click_threshold && diff_y < click_threshold) {
              // C'est un clic court (mouvement très faible)
              ESP_LOGI(TAG, "Loop button clicked");
              char message[10];
              snprintf(message, sizeof(message), "t,lo,0");
              sendMessage(message);
          } else if (diff_x > swipe_threshold_x && diff_x > diff_y * 2) { // S'assurer que le mouvement horizontal est dominant
              // C'est un swipe horizontal
              if (release_point.x < loop_press_point.x) {
                  // Swipe gauche
                  ESP_LOGI(TAG, "Swipe gauche détecté sur le bouton Loop");
                  char message[10];
                  snprintf(message, sizeof(message), "t,li,0");
                  sendMessage(message);
              } else {
                  // Swipe droite
                  ESP_LOGI(TAG, "Swipe droite détecté sur le bouton Loop");
                  char message[10];
                  snprintf(message, sizeof(message), "t,lr,0");
                  sendMessage(message);
              }
          }
          // Ignorer les autres types de gestes (par ex. swipe vertical) ou mouvements intermédiaires
      }
}

void loop_start_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Loop start button clicked");
            char message[10];
            snprintf(message, sizeof(message), "t,ls,0");
            sendMessage(message);            
      }
}

void add_cue_button_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Add cue button clicked");
            char message[10];
            snprintf(message, sizeof(message), "t,ac,0");
            sendMessage(message);            
      }
      else if (code == LV_EVENT_LONG_PRESSED) {
            ESP_LOGI(TAG, "Add cue button long pressed - showing cues popup");
            show_cues_popup();
      }
}

void tempo_label_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Tempo label clicked");
            char message[10];
            snprintf(message, sizeof(message), "t,te,0");
            sendMessage(message);
      }
}

void loop_start_label_event_cb(lv_event_t *e)
{
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_LONG_PRESSED) {
            ESP_LOGI(TAG, "Loop Start label long pressed - showing popup");
            show_loop_length_popup();
      }
}

// Gestionnaires d'événements pour le popup loop length
void loop_length_popup_background_event_cb(lv_event_t *e) {
      lv_obj_t *target = lv_event_get_target(e);
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_SHORT_CLICKED) {
            // Si on clique sur le fond, fermer le popup
            if (target == ui.loop_length_popup.background_container) {
                  hide_loop_length_popup();
            }
      }
}

void loop_length_roller_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);
      lv_obj_t *obj = lv_event_get_target(e);
      
      if (code == LV_EVENT_VALUE_CHANGED) {
            char buf[32];
            lv_roller_get_selected_str(obj, buf, sizeof(buf));
            
            // Identifier quel roller a changé
            if (obj == ui.loop_length_popup.bar.roller) {
                  ESP_LOGI(TAG, "Bar roller changé: %s", buf);
            } else if (obj == ui.loop_length_popup.beat.roller) {
                  ESP_LOGI(TAG, "Beat roller changé: %s", buf);
            } else if (obj == ui.loop_length_popup.subdivision.roller) {
                  ESP_LOGI(TAG, "Subdivision roller changé: %s", buf);
            }
      }
}

void loop_length_ok_button_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);
      
      if (code == LV_EVENT_SHORT_CLICKED) {
            // Récupérer les valeurs sélectionnées
            uint16_t bar_selected = lv_roller_get_selected(ui.loop_length_popup.bar.roller);
            uint16_t beat_selected = lv_roller_get_selected(ui.loop_length_popup.beat.roller);
            uint16_t subdivision_selected = lv_roller_get_selected(ui.loop_length_popup.subdivision.roller);
            
            // Récupérer les valeurs de configuration
            int max_beats_per_bar = 0;
            int max_subdivisions_per_beat = 0;
            int sig_denom = 0;
            get_loop_length_values(&max_beats_per_bar, &max_subdivisions_per_beat);
            get_signature_denominator(&sig_denom);
            
            ESP_LOGI(TAG, "Loop length OK clicked - Bar: %d, Beat: %d, Subdivision: %d", 
                     bar_selected, beat_selected, subdivision_selected);
            
            // Calculer la valeur float selon la formule demandée
            // bars * beats_per_bar + beats + subdivisions / subdivisions_per_beat
            float total_beats = 0.0f;
            
            // Ajouter les bars complets (bar_selected * beats_per_bar)
            total_beats += (float)bar_selected * max_beats_per_bar;
            
            // Ajouter les beats
            total_beats += (float)beat_selected;
            
            // Ajouter les subdivisions converties en fraction de beat
            if (max_subdivisions_per_beat > 0) {
                total_beats += (float)subdivision_selected / (float)max_subdivisions_per_beat;
            }
            
            // Conversion pour Live : Live normalise tout vers des noires (quarter notes)
            // valeur_live = beats_calculés × (4.0 / dénominateur_mesure)
            float conversion_factor = 4.0f / (float)sig_denom;
            float total_beats_for_live = total_beats * conversion_factor;
            
            ESP_LOGI(TAG, "Valeur calculée: %.3f beats, après conversion Live: %.3f (bars=%d, beats=%d, subdivisions=%d, max_beats_per_bar=%d, max_subdivisions_per_beat=%d, sig_denom=%d)", 
                     total_beats, total_beats_for_live, bar_selected, beat_selected, subdivision_selected, 
                     max_beats_per_bar, max_subdivisions_per_beat, sig_denom);
            
            // Envoyer le message avec la valeur float convertie pour Live
            char message[50];
            snprintf(message, sizeof(message), "t,ll,%.3f", total_beats_for_live);
            sendMessage(message);
            
            // Fermer le popup
            hide_loop_length_popup();
      }
}

// Gestionnaires d'événements pour le popup des cues
void cues_popup_background_event_cb(lv_event_t *e) {
      lv_obj_t *target = lv_event_get_target(e);
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_SHORT_CLICKED) {
            // Si on clique sur le fond, fermer le popup
            if (target == ui.cues_popup.background_container) {
                  hide_cues_popup();
            }
      }
}

void cue_button_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);

      if (code == LV_EVENT_CLICKED) {
            // Récupérer l'index du cue (user data)
            int cue_index = (int)(intptr_t)lv_event_get_user_data(e);

            ESP_LOGI(TAG, "Cue button %d clicked", cue_index);

            // Envoyer le message au format t,lc,<index>
            char message[20];
            snprintf(message, sizeof(message), "t,lc,%d", cue_index);
            sendMessage(message);

            // Fermer le popup après sélection
            hide_cues_popup();
      }
}

// Event callback for loop settings popup background
void loop_settings_popup_background_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_CLICKED) {
            ESP_LOGI(TAG, "Loop settings popup background clicked - hiding popup");
            hide_loop_settings_popup();
      }
}

// Event callback for "Set loop start here" button
void loop_settings_set_start_button_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Loop start button clicked");
            char message[10];
            snprintf(message, sizeof(message), "t,ls,0");
            sendMessage(message);
            hide_loop_settings_popup();
      }
}

// Event callback for "Set loop length" button
void loop_settings_set_length_button_event_cb(lv_event_t *e) {
      lv_event_code_t code = lv_event_get_code(e);
      if (code == LV_EVENT_SHORT_CLICKED) {
            ESP_LOGI(TAG, "Set loop length button clicked - showing loop length popup");
            hide_loop_settings_popup();
            show_loop_length_popup();
      }
}