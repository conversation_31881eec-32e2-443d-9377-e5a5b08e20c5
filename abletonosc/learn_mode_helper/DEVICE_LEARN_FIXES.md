# Corrections des problèmes avec les devices en mode learn

## Problème identifié (CORRECTION)

Après analyse plus approfondie, le vrai problème était dans la gestion du `track_index = -2` (ChainTrackIndex) dans `start_learn_listen()`.

### Fonctionnement correct du learn mode

1. **`device_learn.py`** : Met en place des listeners **temporaires** sur tous les devices quand on entre en mode learn
2. **Détection** : Ces listeners détectent quel paramètre l'utilisateur bouge dans Live
3. **Message OSC** : `device_learn.py` envoie `/live/device/learning/parameter/value` vers Go
4. **Go** : Traite le message et envoie `/live/learn/slot` vers Python avec `(slotIndex, ParamTypeDevice, ChainTrackIndex=-2, deviceIndex, paramIndex)`
5. **`learn_mode_helper`** : Reçoit le message et crée un listener **permanent** pour ce paramètre dans le slot

### Cause racine du problème

Le message `/live/learn/slot` envoyé par Go contient `track_index = -2` (ChainTrackIndex), mais `start_learn_listen()` ne gérait pas ce cas spécial :

```python
# AVANT (problématique)
if track_index == -1 and self.manager.trackLock and self.manager.lockedTrack:
    learned_track = self.manager.lockedTrack
else:
    all_tracks = self.manager.get_visible_tracks()
    if 0 <= track_index < len(all_tracks):  # -2 n'est pas dans cette plage !
        learned_track = all_tracks[track_index]
# learned_track reste None pour track_index = -2
```

Résultat : `learned_track` était `None`, causant l'échec de la création du listener permanent.

## Corrections apportées

### 1. Correction principale : Gestion de `track_index = -2`

**Fichier** : `learn_mode_osc_actions.py`

```python
# APRÈS (corrigé)
if track_index == -1 and self.manager.trackLock and self.manager.lockedTrack:
    learned_track = self.manager.lockedTrack
# NOUVEAU: Gestion spéciale pour track_index = -2 (ChainTrackIndex)
elif track_index == -2:
    # Pour les devices et chains, on utilise la piste sélectionnée
    learned_track = self.song.view.selected_track
    if not learned_track:
        self.logger.error("Aucune piste sélectionnée pour track_index = -2")
        return
else:
    # Comportement existant pour les autres cas
    all_tracks = self.manager.get_visible_tracks()
    if 0 <= track_index < len(all_tracks):
        learned_track = all_tracks[track_index]
```

### 2. Amélioration de la logique device

**Fichier** : `learn_mode_osc_actions.py`

- Utilisation de la `learned_track` déjà déterminée au lieu de refaire la logique
- Meilleure gestion des erreurs avec logging détaillé
- Vérification des indices de device et paramètre

### 3. Ajout de logging détaillé

**Fichier** : `learn_mode_osc_actions.py`

- Logging des paramètres reçus
- Logging de la résolution des tracks et devices
- Logging de la création des listeners
- Facilite le diagnostic des problèmes

```python
def disable_parameter_listener(self, device, param_index):
    """Désactive temporairement un listener spécifique"""

def enable_parameter_listener(self, listener_key):
    """Réactive un listener précédemment désactivé"""
```

## Architecture de la solution

```
Learn Mode Slot Assignment
         ↓
_check_duplicates()
         ↓
_resolve_device_learn_conflicts()
         ↓
[Désactive listeners device_learn.py]
         ↓
_setup_device_parameter_listener()
         ↓
[Crée listener spécifique au slot]
```

## Avantages de cette approche

1. **Pas de modification majeure** de l'architecture existante
2. **Compatibilité préservée** avec les deux systèmes
3. **Résolution automatique** des conflits
4. **Logging détaillé** pour le débogage
5. **Gestion d'erreurs robuste**

## Tests recommandés

1. **Test UI → Slot** : Sélectionner un paramètre depuis l'UI puis assigner à un slot
2. **Test Live → Slot** : Mettre un slot en learning puis bouger un paramètre dans Live
3. **Test doublons** : Essayer d'assigner le même paramètre à plusieurs slots
4. **Test suppression** : Supprimer un slot et vérifier le nettoyage
5. **Test changement device** : Changer la liste des devices et vérifier la stabilité

## Notes importantes

- Les corrections sont **non-destructives** et préservent la fonctionnalité existante
- Le système de logging permet de diagnostiquer les problèmes restants
- La méthode `_resolve_device_learn_conflicts()` peut être étendue si nécessaire
