"""
Script de test pour vérifier la résolution des conflits entre device_learn.py et learn_mode_helper
Ce script peut être utilisé pour diagnostiquer les problèmes de listeners en mode learn
"""

def test_device_learn_conflicts(learn_handler):
    """
    Teste la résolution des conflits entre les listeners
    
    Args:
        learn_handler: Instance de LearnModeHandler
    """
    print("=== TEST: Résolution des conflits device_learn ===")
    
    # Vérifier que les méthodes existent
    required_methods = [
        '_resolve_device_learn_conflicts',
        '_check_duplicates', 
        '_clean_slot_listeners',
        '_debug_listeners_state'
    ]
    
    for method_name in required_methods:
        if hasattr(learn_handler, method_name):
            print(f"✓ Méthode {method_name} disponible")
        else:
            print(f"✗ Méthode {method_name} MANQUANTE")
    
    # Vérifier l'accès au device_learn handler
    if hasattr(learn_handler.manager, 'device_learn'):
        device_learn = learn_handler.manager.device_learn
        print("✓ device_learn handler accessible")
        
        if hasattr(device_learn, 'learn_listeners'):
            print(f"✓ device_learn.learn_listeners disponible ({len(device_learn.learn_listeners)} listeners)")
        else:
            print("✗ device_learn.learn_listeners MANQUANT")
            
        if hasattr(device_learn, 'disable_parameter_listener'):
            print("✓ device_learn.disable_parameter_listener disponible")
        else:
            print("✗ device_learn.disable_parameter_listener MANQUANT")
            
    else:
        print("✗ device_learn handler NON ACCESSIBLE")
    
    # Afficher l'état actuel des listeners
    print(f"\nÉtat actuel:")
    print(f"- learn_mode listeners: {len(learn_handler.learn_listeners)}")
    
    if hasattr(learn_handler.manager, 'device_learn'):
        device_learn_count = len(learn_handler.manager.device_learn.learn_listeners)
        print(f"- device_learn listeners: {device_learn_count}")
    
    print("=== FIN TEST ===")


def debug_listener_conflicts(learn_handler, device, param_index):
    """
    Debug les conflits de listeners pour un device/paramètre spécifique
    
    Args:
        learn_handler: Instance de LearnModeHandler
        device: Device Ableton Live
        param_index: Index du paramètre
    """
    print(f"=== DEBUG: Conflits pour {device.name} param {param_index} ===")
    
    # Chercher dans learn_mode listeners
    learn_conflicts = []
    for key, callback in learn_handler.learn_listeners.items():
        if f"param_{param_index}" in key:
            learn_conflicts.append(key)
    
    print(f"Learn mode listeners conflictuels: {learn_conflicts}")
    
    # Chercher dans device_learn listeners
    if hasattr(learn_handler.manager, 'device_learn'):
        device_learn = learn_handler.manager.device_learn
        device_conflicts = []
        
        for key, callback in device_learn.learn_listeners.items():
            if key.endswith(f"_param_{param_index}"):
                try:
                    if (hasattr(callback, 'parameter') and 
                        callback.parameter in device.parameters and
                        device.parameters.index(callback.parameter) == param_index):
                        device_conflicts.append(key)
                except:
                    pass
        
        print(f"Device learn listeners conflictuels: {device_conflicts}")
    
    print("=== FIN DEBUG ===")


def simulate_learn_conflict_resolution(learn_handler, learn_slot, device, param_index):
    """
    Simule la résolution d'un conflit lors de l'assignation d'un slot
    
    Args:
        learn_handler: Instance de LearnModeHandler
        learn_slot: Numéro du slot
        device: Device Ableton Live  
        param_index: Index du paramètre
    """
    print(f"=== SIMULATION: Résolution conflit slot {learn_slot} ===")
    
    # État avant
    print("AVANT résolution:")
    debug_listener_conflicts(learn_handler, device, param_index)
    
    # Appeler la méthode de résolution
    try:
        if hasattr(learn_handler, '_resolve_device_learn_conflicts'):
            learn_handler._resolve_device_learn_conflicts(device, param_index)
            print("✓ _resolve_device_learn_conflicts exécuté")
        else:
            print("✗ _resolve_device_learn_conflicts non disponible")
    except Exception as e:
        print(f"✗ Erreur lors de la résolution: {e}")
    
    # État après
    print("\nAPRÈS résolution:")
    debug_listener_conflicts(learn_handler, device, param_index)
    
    print("=== FIN SIMULATION ===")


# Exemple d'utilisation (à adapter selon le contexte)
"""
# Dans le contexte d'Ableton Live:
learn_handler = manager.learn_mode_handler  # ou équivalent
selected_track = song.view.selected_track
if selected_track and selected_track.devices:
    device = selected_track.devices[0]
    param_index = 1
    
    # Tests
    test_device_learn_conflicts(learn_handler)
    debug_listener_conflicts(learn_handler, device, param_index)
    simulate_learn_conflict_resolution(learn_handler, 0, device, param_index)
"""
