package livelearnmode

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/types"
	"oscbridge/Live/utils"
)

// LiveLearnMode gère le mode learn de Live
type LiveLearnMode struct {
	*communication.BaseMode
	trackManager      *live.LiveTrackManager
	commManager       *communication.CommunicationManager
	displayManager    *LiveLearnDisplayManager
	state             *LiveLearnModeState
	deviceParameters  map[int]*DeviceParameters
	isActive          bool
	volumeConverter   *utils.VolumeConverter
	volumeSendConverter *utils.VolumeSendConverter

	// Champ pour la gestion du handler hardware
	hardwareHandler    func(communication.HardwareEvent)

	// Champs pour la gestion des conflits entre mute/solo pour les chaînes
	lastMuteTime  int64
	lastMuteSlot  *int
	muteDebounceTime int64 // Délai (ms) pour attendre avant de traiter le mute de chaîne
}

// NewLiveLearnMode crée une nouvelle instance du mode learn
func NewLiveLearnMode(trackManager *live.LiveTrackManager, commManager *communication.CommunicationManager) *LiveLearnMode {
	state := NewLiveLearnModeState()

	mode := &LiveLearnMode{
		BaseMode:           communication.NewBaseMode(),
		trackManager:       trackManager,
		commManager:        commManager,
		state:              state,
		deviceParameters:   make(map[int]*DeviceParameters),
		isActive:           false,
		volumeConverter:    utils.NewVolumeConverter(),
		volumeSendConverter: utils.NewVolumeSendConverter(),
		muteDebounceTime:   100, // 100ms de délai pour le debounce
	}

	// Créer le gestionnaire d'affichage
	mode.displayManager = NewLiveLearnDisplayManager(commManager, trackManager, state)
	mode.displayManager.SetParentMode(mode)

	return mode
}

// Initialize initialise le mode learn
func (m *LiveLearnMode) Initialize(service communication.OscService) {
	log.Println("Initialisation du mode learn...")

	// Initialiser le mode de base
	m.BaseMode.Initialize(service)

	// Nettoyer tous les slots pour s'assurer d'un état initial propre
	m.ClearAllSlots()

	// Enregistrer tous les handlers OSC
	m.registerAllOSCHandlers()

	// Enregistrer les écouteurs d'événements
	m.registerEventHandlers()

	// Envoyer le message initial pour mettre à jour le mode learn
	m.BaseMode.Send(OscAddressLearnStop)

	log.Println("Mode learn initialisé avec succès.")
}

// ClearAllSlots nettoie tous les slots en arrière-plan
func (m *LiveLearnMode) ClearAllSlots() {
	log.Println("Nettoyage de tous les slots...")

	// Verrouiller l'état pour éviter les accès concurrents
	m.state.Mutex.Lock()

	// Réinitialiser tous les slots
	for i := 0; i < 32; i++ {
		m.state.Slots[i] = NewSlotInfo(i)

		// Si le mode est actif, mettre à jour l'affichage pour les slots de la page courante

			slotPage := (i / SlotsPerPage) + 1
			if slotPage == m.state.CurrentPage {
				displaySlot := i % SlotsPerPage
				m.commManager.SendMessage(fmt.Sprintf("ls,%d,null,-,-,-,-,-", displaySlot), true)

		}
	}

	// Réinitialiser les buffers d'encoders
	m.state.EncoderBuffers = make(map[int]float64)
	log.Println("Buffers d'encoders réinitialisés")

	m.state.Mutex.Unlock()

	// Vider le cache des messages lo pour tous les slots
	LoMessageMutex.Lock()
	LastSentLoMessages = make(map[int]string)
	LoMessageMutex.Unlock()
	log.Println("Cache des messages lo vidé pour tous les slots")

	log.Println("Tous les slots ont été nettoyés.")
}

// Activate active le mode learn
func (m *LiveLearnMode) Activate() {
	log.Println("\n=== Activating LearnMode ===")
	m.BaseMode.Activate()
	m.isActive = true

	// Configurer le handler hardware
	m.hardwareHandler = m.HandleHardwareEvent
	m.commManager.AddHardwareEventHandler(m.hardwareHandler)

	// Vérifier l'état des slots avant la mise à jour de l'affichage
	log.Println("Vérification de l'état des slots avant mise à jour de l'affichage...")
	m.state.Mutex.RLock()
	for i := 0; i < 32; i++ {
		if slot, exists := m.state.Slots[i]; exists && slot.Type != nil && *slot.Type == ParamTypeDevice {
			log.Printf("DEBUG: Activate - Slot %d (device): paramName: %s, value: %v, valueString: '%s'",
				i, slot.ParameterName, slot.Value, slot.ValueString)
		}
	}
	m.state.Mutex.RUnlock()

	// Réinitialiser le cache des messages lo envoyés
	LoMessageMutex.Lock()
	LastSentLoMessages = make(map[int]string)
	LoMessageMutex.Unlock()
	log.Println("Cache des messages lo réinitialisé")

	// Activer l'affichage du mode learn
	log.Println("Mise à jour de l'affichage...")
	m.displayManager.UpdateModeDisplay()
	m.displayManager.UpdatePage(m.state.CurrentPage)
	// Note: UpdateAllSlots n'est pas nécessaire ici car les handlers
	// maintiennent déjà l'état à jour. UpdateAllSlots est principalement utile
	// lors des changements de page.

	// Envoyer les messages de configuration
	m.sendStartListen()

	log.Println("=== LearnMode Activation Complete ===")
}

// Deactivate désactive le mode learn (utilisé lors du changement de mode)
func (m *LiveLearnMode) Deactivate() {
	log.Println("\n=== Deactivating LearnMode ===")
	m.BaseMode.Deactivate()
	m.isActive = false

	// Si en mode apprentissage, l'arrêter
	if m.state.IsLearning && m.state.ActiveSlot != nil {
		m.state.IsLearning = false
		m.displayManager.UpdateLearningStatus(false, 0)
		m.BaseMode.Send(OscAddressLearnDelSlot, []interface{}{*m.state.ActiveSlot})
	}

	// Supprimer le handler hardware
	if m.hardwareHandler != nil {
		m.commManager.RemoveHardwareEventHandler(m.hardwareHandler)
		m.hardwareHandler = nil
	}

	m.sendStopListen()

	log.Println("=== LearnMode Deactivation Complete ===")
}

// CleanupForExit nettoie complètement le mode learn (utilisé lors de la fermeture de l'application)
func (m *LiveLearnMode) CleanupForExit() {
	log.Println("\n=== LearnMode Complete Cleanup Start ===")

	// Désactiver le mode si actif
	if m.isActive {
		m.Deactivate()
	}

	// Supprimer tous les handlers OSC
	m.unregisterAllOSCHandlers()

	// Supprimer les écouteurs d'événements du trackManager
	m.unregisterEventHandlers()

	// Nettoyer les buffers d'encoders
	m.state.Mutex.Lock()
	m.state.EncoderBuffers = make(map[int]float64)
	m.state.Mutex.Unlock()
	log.Println("Buffers d'encoders nettoyés")

	// Envoyer le message d'arrêt
	m.BaseMode.Send(OscAddressLearnStop)

	log.Println("=== LearnMode Complete Cleanup Complete ===")
}

// IsActive retourne si le mode est actif
func (m *LiveLearnMode) IsActive() bool {
	return m.isActive
}

// SetPage change la page courante
func (m *LiveLearnMode) SetPage(page int) {
	if page < 1 || page > 4 {
		return
	}

	m.state.Mutex.Lock()
	m.state.CurrentPage = page
	m.state.Mutex.Unlock()

	m.displayManager.UpdatePage(page)
	m.displayManager.UpdateAllSlots()
}

// PageUp passe à la page suivante
func (m *LiveLearnMode) PageUp() {
	if m.state.CurrentPage < 4 {
		m.SetPage(m.state.CurrentPage + 1)
	}
}

// PageDown passe à la page précédente
func (m *LiveLearnMode) PageDown() {
	if m.state.CurrentPage > 1 {
		m.SetPage(m.state.CurrentPage - 1)
	}
}

// StartLearning démarre l'apprentissage pour un slot spécifique
func (m *LiveLearnMode) StartLearning(slotIndex int) {
	if slotIndex < 0 || slotIndex >= 32 {
		return
	}

	// Si déjà en apprentissage, arrêter l'apprentissage précédent
	if m.state.IsLearning && m.state.ActiveSlot != nil {
		m.BaseMode.Send(OscAddressLearnDelSlot, []interface{}{*m.state.ActiveSlot})
	}

	// Mettre à jour l'état
	m.state.Mutex.Lock()
	m.state.IsLearning = true
	m.state.ActiveSlot = &slotIndex
	m.state.Mutex.Unlock()

	// Mettre à jour l'affichage
	m.displayManager.UpdateLearningStatus(true, slotIndex)

	// Envoyer les messages de configuration
	//m.sendStartListen()
}

// StopLearning arrête l'apprentissage
func (m *LiveLearnMode) StopLearning() {
	if !m.state.IsLearning || m.state.ActiveSlot == nil {
		return
	}

	// Envoyer le message d'arrêt
	m.BaseMode.Send(OscAddressLearnDelSlot, []interface{}{*m.state.ActiveSlot})

	// Mettre à jour l'état
	m.state.Mutex.Lock()
	m.state.IsLearning = false
	m.state.ActiveSlot = nil
	m.state.Mutex.Unlock()

	// Mettre à jour l'affichage
	m.displayManager.UpdateLearningStatus(false, 0)
}

// ClearSlot efface un slot spécifique
func (m *LiveLearnMode) ClearSlot(slotIndex int) {
	if slotIndex < 0 || slotIndex >= 32 {
		return
	}

	// Mettre à jour l'état
	m.state.Mutex.Lock()
	if _, exists := m.state.Slots[slotIndex]; exists {
		m.state.Slots[slotIndex] = NewSlotInfo(slotIndex)
	}
	m.state.Mutex.Unlock()

	// Vider le cache des messages lo pour ce slot
	LoMessageMutex.Lock()
	delete(LastSentLoMessages, slotIndex)
	LoMessageMutex.Unlock()
	log.Printf("ClearSlot: cache des messages lo vidé pour le slot %d", slotIndex)

	// Mettre à jour l'affichage
	m.displayManager.ClearSlot(slotIndex)

	// Envoyer le message d'effacement
	m.BaseMode.Send(OscAddressLearnDelSlot, []interface{}{slotIndex})
}

// UpdateSlotInfoOnly met à jour uniquement les informations d'un slot sans mettre à jour l'affichage
func (m *LiveLearnMode) UpdateSlotInfoOnly(slotIndex int, updates map[string]interface{}) *SlotInfo {
	if slotIndex < 0 || slotIndex >= 32 {
		return nil
	}

	// Récupérer le slot existant ou en créer un nouveau
	m.state.Mutex.Lock()
	defer m.state.Mutex.Unlock()

	slot, exists := m.state.Slots[slotIndex]
	if !exists {
		slot = NewSlotInfo(slotIndex)
		m.state.Slots[slotIndex] = slot
	}

	// Appliquer les mises à jour
	for key, value := range updates {
		switch key {
		case "type":
			if typeVal, ok := value.(int); ok {
				slot.Type = &typeVal
			}
		case "trackColor":
			if colorVal, ok := value.(string); ok {
				slot.TrackColor = colorVal
			}
		case "trackName":
			if nameVal, ok := value.(string); ok {
				slot.TrackName = nameVal
			}
		case "deviceName":
			if nameVal, ok := value.(string); ok {
				slot.DeviceName = nameVal
			}
		case "parameterName":
			if nameVal, ok := value.(string); ok {
				slot.ParameterName = nameVal
			}
		case "parameterIndex":
			if indexVal, ok := value.(int); ok {
				slot.ParameterIndex = &indexVal
			}
		case "sendIndex":
			if indexVal, ok := value.(int); ok {
				slot.SendIndex = &indexVal
			}
		case "deviceIndex":
			if indexVal, ok := value.(int); ok {
				slot.DeviceIndex = &indexVal
			}
		case "chainPath":
			if pathVal, ok := value.([]int); ok {
				slot.ChainPath = pathVal
			}
		case "value":
			if valFloat, ok := value.(float64); ok {
				slot.Value = &valFloat
			}
		case "valueString":
			if strVal, ok := value.(string); ok {
				slot.ValueString = strVal
			}
		case "isQuantized":
			if quantVal, ok := value.(bool); ok {
				slot.IsQuantized = &quantVal
			}
		case "min":
			if minVal, ok := value.(float64); ok {
				slot.Min = &minVal
			}
		case "max":
			if maxVal, ok := value.(float64); ok {
				slot.Max = &maxVal
			}
		}
	}

	return slot
}

// UpdateSlotInfo met à jour les informations d'un slot et met à jour l'affichage
// Cette fonction est maintenant obsolète et sera supprimée dans une future version
// Utilisez UpdateSlotInfoOnly pour mettre à jour l'état interne uniquement
// et gérez l'affichage séparément si nécessaire
func (m *LiveLearnMode) UpdateSlotInfo(slotIndex int, updates map[string]interface{}) {
	log.Printf("DEPRECATED: UpdateSlotInfo est obsolète, utilisez UpdateSlotInfoOnly à la place")

	// Mettre à jour l'état interne
	slot := m.UpdateSlotInfoOnly(slotIndex, updates)
	if slot == nil {
		return
	}

	// Calculer la page et l'index relatif pour l'affichage
	slotPage := (slotIndex / SlotsPerPage) + 1
	displaySlot := slotIndex % SlotsPerPage
	isOnCurrentPage := slotPage == m.state.CurrentPage

	// Mettre à jour l'affichage si le slot est sur la page courante
	if isOnCurrentPage {
		// Envoyer les messages d'affichage
		paramName := "Mute" // Valeur par défaut
		if slot.ParameterName != "" {
			paramName = slot.ParameterName
		}

		// Formater la valeur
		formattedValue := "000"
		displayValue := "Off"
		if slot.Value != nil && *slot.Value > 0 {
			formattedValue = "100"
			displayValue = "On"
		}

		// Envoyer le message ls
		m.commManager.SendMessage(
			fmt.Sprintf("ls,%d,%s,%s,%s", displaySlot, paramName, formattedValue, displayValue),
			m.isActive,
		)

		// Envoyer le message lo
		deviceNameToSend := ""
		if slot.DeviceName != "" {
			deviceNameToSend = fmt.Sprintf("• %s", slot.DeviceName)
		}

		m.commManager.SendMessage(
			fmt.Sprintf("lo,%d,%s,%s,%s", displaySlot, deviceNameToSend, slot.TrackName, slot.TrackColor),
			m.isActive,
		)
	}
}

// sendStartListen envoie les messages de configuration pour l'apprentissage
func (m *LiveLearnMode) sendStartListen() {
	log.Println("Sending start_listen messages")
	m.BaseMode.Send(OscAddressLearnSetupTrack, []interface{}{})
	m.BaseMode.Send(OscAddressLearnSetupDevice, []interface{}{})
}

func (m *LiveLearnMode) sendStopListen() {
	log.Println("Sending stop_listen messages")
	m.BaseMode.Send(OscAddressLearnStopTrack, []interface{}{})
	m.BaseMode.Send(OscAddressLearnStopDevice, []interface{}{})
}

// registerEventHandlers enregistre les écouteurs d'événements pour le LiveTrackManager
func (m *LiveLearnMode) registerEventHandlers() {
	m.trackManager.On("selectedTrackDeviceUpdate", m.onSelectedTrackChange)
	m.trackManager.On("returnTracksNameChange", m.onReturnTracksNameChange)
	m.trackManager.On("handleTrackCountUpdate", m.onTrackCountUpdate)
}

// unregisterEventHandlers supprime les écouteurs d'événements pour le LiveTrackManager
func (m *LiveLearnMode) unregisterEventHandlers() {
	m.trackManager.RemoveListener("selectedTrackDeviceUpdate", m.onSelectedTrackChange)
	m.trackManager.RemoveListener("returnTracksNameChange", m.onReturnTracksNameChange)
	m.trackManager.RemoveListener("handleTrackCountUpdate", m.onTrackCountUpdate)
}

// onSelectedTrackChange est appelé lorsque la piste sélectionnée change
func (m *LiveLearnMode) onSelectedTrackChange(args []interface{}) {
	// Pas besoin de faire quoi que ce soit de spécial ici pour le mode learn
	// Mais on pourrait mettre à jour l'affichage si nécessaire
}

// onReturnTracksNameChange est appelé lorsque les noms des pistes de retour changent
func (m *LiveLearnMode) onReturnTracksNameChange(args []interface{}) {
	// Pas besoin de faire quoi que ce soit de spécial ici pour le mode learn
}

// onTrackCountUpdate est appelé lorsque le nombre de pistes change
func (m *LiveLearnMode) onTrackCountUpdate(args []interface{}) {
	// Pas besoin de faire quoi que ce soit de spécial ici pour le mode learn
}



// handleOscMessage est la fonction principale de traitement des messages OSC
// Elle vérifie si nous sommes en mode apprentissage et redirige les messages en conséquence
func (m *LiveLearnMode) handleOscMessage(args []interface{}, address string) {
	log.Printf("[DEBUG] LiveLearnMode handleOscMessage - Start - isLearning: %v, address: %s", m.state.IsLearning, address)

	// Calculer l'index réel de la piste (comme dans le code JavaScript)
	var realTrackIndex int
	if len(args) > 0 {
		if trackArg, ok := ParseOscInt(args[0]); ok {
			realTrackIndex = int(math.Abs(float64(trackArg))) - 1
			log.Printf("Calculated realTrackIndex: %d from trackArg: %d", realTrackIndex, trackArg)
		}
	}

	// Si nous sommes en mode apprentissage, traiter les messages d'apprentissage
	m.state.Mutex.RLock()
	isLearning := m.state.IsLearning
	m.state.Mutex.RUnlock()

	if isLearning {
		// Traiter les messages d'apprentissage
		switch address {
		case OscAddressTrackLearningVolume:
			m.handleTrackLearningVolume(args)
			return // Retourner immédiatement après avoir traité le message
		case OscAddressTrackLearningPanning:
			m.handleTrackLearningPanning(args)
			return
		case OscAddressTrackLearningSends:
			m.handleTrackLearningSends(args)
			return
		case OscAddressTrackLearningMute:
			m.handleTrackLearningMute(args)
			return
		case OscAddressTrackLearningSolo:
			m.handleTrackLearningSolo(args)
			return
		case OscAddressDeviceLearningParamValue:
			m.handleDeviceLearningParamValue(args)
			return
		case OscAddressDeviceLearningParamValueStr:
			m.handleDeviceLearningParamValueStr(args)
			return
		case OscAddressChainLearningVolume:
			m.handleChainLearningVolume(args)
			return
		case OscAddressChainLearningPanning:
			m.handleChainLearningPanning(args)
			return
		case OscAddressChainLearningMute:
			m.handleChainLearningMute(args)
			return
		case OscAddressChainLearningSolo:
			m.handleChainLearningSolo(args)
			return
		case OscAddressGetLockedDeviceParamProps:
			m.handleGetLockedDeviceParamProps(args)
			return
		// Traiter également les messages de paramètres de device en mode apprentissage
		case OscAddressDeviceLearningBulkParams:
			log.Printf("[DEBUG] Appel de handleDeviceParameters pour bulk_parameters (isLearning=true)")
			m.handleDeviceParameters(args, address)
			return
		case OscAddressDeviceLearningName:
			m.handleDeviceParameters(args, address)
			return
		}
	}

	// Si nous ne sommes pas en mode apprentissage, traiter les autres messages
	switch address {
	case OscAddressDeviceLearningBulkParams:
		// Traiter les paramètres de device
		log.Printf("[DEBUG] Appel de handleDeviceParameters pour bulk_parameters")
		m.handleDeviceParameters(args, address)
		return
	case OscAddressDeviceLearningName:
		// Traiter les paramètres de device
		m.handleDeviceParameters(args, address)
		return

	// Ignorer les messages d'apprentissage si nous ne sommes pas en mode apprentissage
	case OscAddressTrackLearningVolume:
	case OscAddressTrackLearningPanning:
	case OscAddressTrackLearningSends:
	case OscAddressTrackLearningMute:
	case OscAddressTrackLearningSolo:
	case OscAddressDeviceLearningParamValue:
		// Pour les paramètres de device, nous pouvons quand même les traiter pour mettre à jour l'affichage
		// mais sans modifier l'état d'apprentissage
		log.Printf("[DEBUG] Paramètre de device reçu mais pas en mode apprentissage: %s", address)
		return
	case OscAddressDeviceLearningParamValueStr:
	case OscAddressChainLearningVolume:
	case OscAddressChainLearningPanning:
	case OscAddressChainLearningMute:
	case OscAddressChainLearningSolo:
		// Ignorer ces messages si nous ne sommes pas en mode apprentissage
		log.Printf("Ignoring learning message %s because isLearning=false", address)
		return

	// Traiter les messages de mise à jour de paramètres
	case OscAddressTrackLearnGetVolume:
	case OscAddressTrackLearnGetPanning:
	case OscAddressTrackLearnGetSends:
	case OscAddressTrackLearnGetMute:
	case OscAddressTrackLearnGetSolo:
	case OscAddressDeviceLearnGetParamValue:
	case OscAddressChainLearnGetVolume:
	case OscAddressChainLearnGetPanning:
	case OscAddressChainLearnGetMute:
	case OscAddressChainLearnGetSolo:
	case OscAddressGetLockedDeviceParamProps:
		// Traiter les mises à jour de paramètres
		// m.handleParameterUpdates(args, address)
		log.Printf("TODO: Implement handleParameterUpdates for %s", address)

	case OscAddressLearnSlotGetProperties:
		// Traiter les propriétés de slot
		m.handleSlotProperties(args)

	case OscAddressLearnSlotCleared:
		// Traiter l'effacement de slot
		m.handleSlotCleared(args)

	case OscAddressReadyToListen:
		// Traiter le message "prêt à écouter"
		m.handleReadyToListen(args)
	}

	log.Printf("[DEBUG] LiveLearnMode handleOscMessage - End - isLearning: %v", m.state.IsLearning)
}

// SetLearnData définit les données d'apprentissage pour le mode learn
func (m *LiveLearnMode) SetLearnData(data *types.LearnData) {
	// Cette méthode est appelée par le LiveModeManager pour transmettre les données
	// d'apprentissage au mode learn
	if data == nil {
		return
	}

	// Si le mode est actif et en apprentissage, simuler la réception d'un message OSC
	if m.isActive && m.state.IsLearning && m.state.ActiveSlot != nil {
		switch data.ParamType {
		case types.ParamTypeVolume:
			// Simuler un message de volume
			value := float64(0)
			if data.TrackIndex >= 0 {
				m.handleTrackLearningVolume([]interface{}{data.TrackIndex + 1, value})
			}
		case types.ParamTypePan:
			// Simuler un message de panoramique
			value := float64(0)
			if data.TrackIndex >= 0 {
				m.handleTrackLearningPanning([]interface{}{data.TrackIndex + 1, value})
			}
		case types.ParamTypeSend:
			// Simuler un message de send
			value := float64(0)
			if data.TrackIndex >= 0 && data.SendIndex != nil {
				m.handleTrackLearningSends([]interface{}{data.TrackIndex + 1, *data.SendIndex, value})
			}
		case types.ParamTypeMute:
			// Simuler un message de mute
			value := 0
			if data.TrackIndex >= 0 {
				m.handleTrackLearningMute([]interface{}{data.TrackIndex + 1, value})
			}
		case types.ParamTypeSolo:
			// Simuler un message de solo
			value := 0
			if data.TrackIndex >= 0 {
				m.handleTrackLearningSolo([]interface{}{data.TrackIndex + 1, value})
			}
		case types.ParamTypeChainVolume:
			// Simuler un message de volume de chaîne
			value := float64(0)
			if len(data.ChainPath) > 0 {
				jsonChainPath, _ := json.Marshal(data.ChainPath)
				m.handleChainLearningVolume([]interface{}{ChainTrackIndex, string(jsonChainPath), value})
			}
		case types.ParamTypeChainPan:
			// Simuler un message de panoramique de chaîne
			value := float64(0)
			if len(data.ChainPath) > 0 {
				jsonChainPath, _ := json.Marshal(data.ChainPath)
				m.handleChainLearningPanning([]interface{}{ChainTrackIndex, string(jsonChainPath), value})
			}
		case types.ParamTypeChainMute:
			// Simuler un message de mute de chaîne
			value := 0
			if len(data.ChainPath) > 0 {
				jsonChainPath, _ := json.Marshal(data.ChainPath)
				m.handleChainLearningMute([]interface{}{ChainTrackIndex, string(jsonChainPath), value})
			}
		case types.ParamTypeChainSolo:
			// Simuler un message de solo de chaîne
			value := 0
			if len(data.ChainPath) > 0 {
				jsonChainPath, _ := json.Marshal(data.ChainPath)
				m.handleChainLearningSolo([]interface{}{ChainTrackIndex, string(jsonChainPath), value})
			}
		case types.ParamTypeDevice:
			// Simuler un message de paramètre de device
			if data.DeviceIndex != nil && data.ParamIndex != nil {
				// Construire les arguments pour le handler
				args := []interface{}{
					-3, // Valeur spéciale pour indiquer un device normal
					*data.DeviceIndex,
					*data.ParamIndex,
					0.5, // Valeur par défaut
					"0.5", // Chaîne de valeur par défaut
				}

				// Si nous avons des informations supplémentaires, les utiliser
				if data.IsQuantized != nil && data.MinValue != nil && data.MaxValue != nil {
					// Adapter la valeur en fonction des min/max
					defaultValue := (*data.MinValue + *data.MaxValue) / 2
					defaultValueStr := fmt.Sprintf("%.1f", defaultValue)

					args[3] = defaultValue
					args[4] = defaultValueStr
				}

				m.handleDeviceLearningParamValue(args)
			}
		}
	}
}
